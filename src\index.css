@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap');

@font-face {
  font-family: 'Brockmann';
  src: url('/brockmann-medium-webfont.ttf') format('truetype'),
       url('/brockmann-medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 24 95% 53%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 24 95% 53%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 24 95% 53%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html,
  body {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    background-color: #ffffff;
    /* Improve touch scrolling on mobile */
    -webkit-overflow-scrolling: touch;
    /* Prevent zoom on input focus on iOS */
    -webkit-text-size-adjust: 100%;
  }

  body {
    @apply bg-white text-black font-sans antialiased;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  /* Fallback to system fonts if SF Pro Display is not available */
  @font-face {
    font-family: "SF Pro Display";
    src: local("SF Pro Display"), local("SFProDisplay-Regular"),
      local("SF Pro Text"), local("SFProText-Regular"), local("Segoe UI"),
      local("Helvetica Neue");
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: "SF Pro Display";
    src: local("SF Pro Display Medium"), local("SFProDisplay-Medium"),
      local("SF Pro Text Medium"), local("SFProText-Medium"),
      local("Segoe UI Semibold"), local("Helvetica Neue Medium");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: "SF Pro Display";
    src: local("SF Pro Display Bold"), local("SFProDisplay-Bold"),
      local("SF Pro Text Bold"), local("SFProText-Bold"), local("Segoe UI Bold"),
      local("Helvetica Neue Bold");
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }
}

/* Animations and Effects */
@layer components {
  .glass-panel {
    @apply bg-white/80 dark:bg-black/30 backdrop-blur-md border border-white/20 dark:border-white/10 shadow-glass;
  }

  .section-transition {
    @apply transition-all duration-700 ease-out;
  }

  .parallax-slow {
    transition: transform 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .parallax-medium {
    transition: transform 0.35s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .parallax-fast {
    transition: transform 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .hover-scale {
    @apply transition-transform duration-300 ease-out hover:scale-105;
  }

  /* Mobile-specific utilities */
  .touch-manipulation {
    touch-action: manipulation;
  }

  .safe-area-inset {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* 3D Chatbot Button Animations */
  .chatbot-siri-pulse {
    animation: siri-pulse 2s ease-in-out infinite;
  }

  .chatbot-breathing {
    animation: breathing 3s ease-in-out infinite;
  }

  .chatbot-glow {
    animation: glow-pulse 2.5s ease-in-out infinite alternate;
  }
}

/* Keyframes for 3D Chatbot Animations */
@keyframes siri-pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
}

@keyframes breathing {
  0%,
  100% {
    transform: scale(1) translateZ(0);
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  }
  50% {
    transform: scale(1.02) translateZ(2px);
    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.4);
  }
}

@keyframes glow-pulse {
  0% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3),
      0 0 40px rgba(59, 130, 246, 0.2), 0 0 60px rgba(59, 130, 246, 0.1);
  }
  100% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5),
      0 0 60px rgba(59, 130, 246, 0.3), 0 0 90px rgba(59, 130, 246, 0.2);
  }
}

@layer components {
  /* Responsive text utilities */
  .text-responsive-xs {
    @apply text-xs sm:text-sm md:text-base;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base md:text-lg;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg md:text-xl;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl md:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl md:text-3xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl md:text-4xl;
  }

  /* Container utilities for better responsive design */
  .container-responsive {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .container-tight {
    @apply w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .text-mask-image {
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    background-size: cover;
    background-position: center;
  }

  .pulse-chip {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-pulse-100 text-pulse-600 border border-pulse-200;
  }

  .section-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12;
  }

  .section-title {
    @apply text-3xl md:text-4xl lg:text-5xl font-display font-bold tracking-tight;
  }

  .section-subtitle {
    @apply text-lg md:text-xl text-gray-600 mt-4 max-w-3xl;
  }

  .glass-card {
    @apply bg-white/70 backdrop-blur-sm border border-white/20 rounded-2xl shadow-elegant transition-all duration-300 hover:shadow-elegant-hover;
  }

  .feature-card {
    @apply p-6 rounded-2xl transition-all duration-500 hover:translate-y-[-5px];
  }

  .button-primary {
    @apply bg-pulse-500 hover:bg-pulse-600 text-white font-medium py-3 px-6 rounded-full transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98];
  }

  .button-secondary {
    @apply bg-transparent border border-gray-300 hover:border-pulse-500 text-gray-800 hover:text-pulse-500 font-medium py-3 px-6 rounded-full transition-all duration-300;
  }

  .nav-link {
    @apply relative text-gray-800 hover:text-pulse-500 py-2 transition-colors duration-300 after:absolute after:bottom-0 after:left-0 after:h-[2px] after:w-0 after:bg-pulse-500 after:transition-all hover:after:w-full;
  }
}

/* Mobile-specific optimizations */
@media (max-width: 640px) {
  /* Improve button touch targets on mobile */
  button,
  a[role="button"],
  .btn,
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Force all sections to be visible */
  section, div[class*="section"], .relative {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  /* Ensure proper overflow handling */
  body, html {
    overflow-x: hidden !important;
  }

  /* Fix hero section specifically */
  .hero-section, [class*="hero"], [class*="carousel"] {
    width: 100vw !important;
    max-width: 100vw !important;
    overflow: hidden !important;
  }

  /* Enhanced client logo section mobile support */
  .client-logos-section {
    -webkit-overflow-scrolling: touch !important;
    overflow-x: hidden !important;
    touch-action: pan-y !important;
  }

  /* Force hardware acceleration for smooth animations */
  .carousel-row, .carousel-track, .logo-item {
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
  }

  /* Enhanced carousel navigation for mobile */
  .carousel-nav-button {
    min-width: 40px !important;
    min-height: 40px !important;
    font-size: 18px !important;
    z-index: 30 !important;
  }

  /* Chat button mobile enhancements */
  .chatbot-mobile {
    bottom: 16px !important;
    right: 16px !important;
    width: 64px !important;
    height: 64px !important;
    z-index: 60 !important;
  }

  /* Prevent navigation overlap */
  .navigation-safe-zone {
    margin-bottom: 20px !important;
    padding-bottom: 20px !important;
  }

  /* Optimize form inputs for mobile */
  input,
  textarea,
  select {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Better spacing for mobile */
  .mobile-spacing {
    padding: 1rem;
  }

  /* Optimize images for mobile */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Fix text alignment issues on mobile */
  .text-justify {
    text-align: left;
  }

  /* Prevent horizontal overflow */
  * {
    max-width: 100%;
    box-sizing: border-box;
  }

  /* Better line height for readability */
  p,
  div,
  span {
    line-height: 1.6;
  }

  /* Ensure proper spacing between elements */
  .space-y-4 > * + * {
    margin-top: 1rem !important;
  }

  .space-y-6 > * + * {
    margin-top: 1.5rem !important;
  }

  /* Fix grid layouts on mobile */
  .grid {
    gap: 1rem !important;
  }

  /* Ensure flex items don't overflow */
  .flex {
    flex-wrap: wrap;
  }

  /* Better padding for containers */
  .container,
  .max-w-7xl,
  .max-w-6xl,
  .max-w-5xl {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Fix carousel height on mobile */
  .hero-carousel {
    min-height: 450px !important;
    max-height: 65vh !important;
  }

  /* Ensure banner images are properly sized */
  .banner-image {
    object-fit: cover !important;
    object-position: center !important;
    width: 100% !important;
    height: 100% !important;
  }

  /* Mobile-specific hero carousel fixes */
  .hero-carousel img {
    object-fit: cover !important;
    object-position: center !important;
    width: 100% !important;
    height: 100% !important;
    min-width: 100% !important;
    min-height: 100% !important;
  }

  /* Ensure all sections are visible on mobile */
  .mobile-section {
    min-height: auto !important;
    padding: 1rem !important;
  }

  /* Fix stats grid on mobile */
  .stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.75rem !important;
  }

  /* Ensure proper text sizing on mobile */
  .mobile-text {
    font-size: 14px !important;
    line-height: 1.5 !important;
  }

  /* Fix viewport issues on mobile browsers */
  .mobile-viewport {
    height: 100vh !important;
    height: 100dvh !important;
  }
}

/* Chatbot animations */
@keyframes chatbot-siri-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
}

@keyframes chatbot-breathing {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.chatbot-siri-pulse {
  animation: chatbot-siri-pulse 2s ease-in-out infinite;
}

.chatbot-breathing {
  animation: chatbot-breathing 3s ease-in-out infinite;
}

.chatbot-glow {
  filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.6));
}

/* Tablet-specific optimizations */
@media (min-width: 641px) and (max-width: 1024px) {
  /* Tablet-specific styles */
  .tablet-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  /* Tablet chat button positioning */
  .chatbot-mobile {
    bottom: 24px !important;
    right: 24px !important;
    width: 56px !important;
    height: 56px !important;
  }
}

/* Desktop optimizations */
@media (min-width: 1025px) {
  /* Desktop-specific styles */
  .desktop-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Optimize for retina displays */
  img {
    image-rendering: -webkit-optimize-contrast;
  }
}

/* Additional responsive utilities for better mobile experience */
@layer utilities {
  /* Responsive text alignment */
  .text-responsive-center {
    @apply text-center sm:text-left;
  }

  .text-responsive-left {
    @apply text-left;
  }

  /* Responsive text sizes */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base md:text-lg;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg md:text-xl;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl md:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl md:text-3xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl;
  }

  /* Responsive spacing utilities */
  .spacing-responsive-sm {
    @apply space-y-2 sm:space-y-3 md:space-y-4;
  }

  .spacing-responsive-md {
    @apply space-y-3 sm:space-y-4 md:space-y-6;
  }

  .spacing-responsive-lg {
    @apply space-y-4 sm:space-y-6 md:space-y-8;
  }

  /* Responsive padding utilities */
  .padding-responsive-sm {
    @apply p-2 sm:p-3 md:p-4;
  }

  .padding-responsive-md {
    @apply p-3 sm:p-4 md:p-6;
  }

  .padding-responsive-lg {
    @apply p-4 sm:p-6 md:p-8;
  }

  /* Responsive margin utilities */
  .margin-responsive-sm {
    @apply m-2 sm:m-3 md:m-4;
  }

  .margin-responsive-md {
    @apply m-3 sm:m-4 md:m-6;
  }

  /* Responsive container utilities */
  .container-responsive {
    @apply w-full px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-20;
  }

  /* Responsive grid utilities */
  .grid-responsive-1-2 {
    @apply grid grid-cols-1 sm:grid-cols-2;
  }

  .grid-responsive-1-2-3 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
  }

  .grid-responsive-1-2-4 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4;
  }

  /* Responsive flex utilities */
  .flex-responsive-col {
    @apply flex flex-col sm:flex-row;
  }

  .flex-responsive-row {
    @apply flex flex-row;
  }

  /* Responsive width utilities */
  .width-responsive-full {
    @apply w-full sm:w-auto;
  }

  .width-responsive-auto {
    @apply w-auto;
  }

  /* Anti-overlap utilities */
  .no-overlap {
    position: relative;
    z-index: 1;
    overflow: hidden;
  }

  .safe-spacing {
    margin: 0.5rem;
    padding: 0.5rem;
  }

  /* Better mobile touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    touch-action: manipulation;
  }

  /* Responsive image utilities */
  .img-responsive {
    width: 100%;
    height: auto;
    max-width: 100%;
    object-fit: cover;
  }

  .img-responsive-contain {
    width: 100%;
    height: auto;
    max-width: 100%;
    object-fit: contain;
  }

  /* Full container image coverage - no gaps */
  .img-full-cover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    min-width: 100%;
    min-height: 100%;
  }

  /* Enhanced image utilities for better visual prominence */
  .img-enhanced {
    filter: contrast(1.05) saturate(1.05) brightness(1.02);
    transition: filter 0.3s ease, transform 0.3s ease;
  }

  .img-enhanced:hover {
    filter: contrast(1.1) saturate(1.1) brightness(1.05);
  }

  /* Responsive viewport utilities */
  .min-h-screen-mobile {
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile browsers */
  }

  .h-screen-mobile {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile browsers */
  }

  /* Better responsive grid utilities */
  .grid-auto-fit {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  .grid-auto-fill {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  /* Responsive aspect ratio utilities */
  .aspect-video-responsive {
    aspect-ratio: 16/9;
  }

  .aspect-square-responsive {
    aspect-ratio: 1/1;
  }

  .aspect-photo-responsive {
    aspect-ratio: 4/3;
  }

  /* Better mobile scroll behavior */
  .scroll-smooth-mobile {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Responsive text truncation */
  .text-truncate-responsive {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .text-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Radial gradient utility for vignette effects */
  .bg-radial-gradient {
    background: radial-gradient(circle at center, var(--tw-gradient-stops));
  }

  /* Enhanced shadow utilities */
  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .shadow-4xl {
    box-shadow: 0 50px 100px -20px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  /* Text wrapping utilities */
  .break-words {
    word-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
  }

  /* Prevent text overflow */
  .text-ellipsis-responsive {
    @apply truncate sm:text-clip;
  }
}

.shimmer {
  @apply relative overflow-hidden before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent;
}

/* Footer divider */
.footer-divider {
  @apply w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent;
}

/* Custom justified text without excessive spacing */
.text-justify-clean {
  text-align: justify;
  text-justify: inter-word;
  text-align-last: left;
  word-spacing: 0;
  letter-spacing: 0;
  hyphens: auto;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  -ms-hyphens: auto;
}

.image-scale-in {
  animation: scaleIn 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@keyframes scaleIn {
  0% {
    transform: scale(0.95);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.fadeIn {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeIn 0.8s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }

.hover-lift {
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
}

/* Parallax effect */
.parallax {
  transform: translateY(var(--parallax-y, 0));
  transition: transform 0.1s ease-out;
}

/* Client logos scrolling animation */
.animate-scroll {
  animation: scroll 30s linear infinite;
}

.animate-scroll:hover {
  animation-play-state: paused;
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}
